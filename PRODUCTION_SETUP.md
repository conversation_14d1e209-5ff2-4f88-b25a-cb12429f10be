# 🚀 Production Setup Guide

This guide walks you through setting up yt2gif.app for production deployment with authentication and payment processing.

## 📋 Prerequisites Checklist

Before starting, ensure you have accounts for:
- [ ] **Database**: Supabase (recommended) or PlanetScale
- [ ] **Hosting**: Vercel (recommended) or Railway
- [ ] **Payments**: Stripe account
- [ ] **OAuth**: Google Cloud Console & GitHub Developer Settings
- [ ] **Monitoring**: Sentry & PostHog (optional but recommended)

## 🗄️ Step 1: Database Setup (Supabase)

### 1.1 Create Supabase Project
1. Go to [supabase.com](https://supabase.com) and create account
2. Create new project
3. Choose region closest to your users
4. Wait for database to initialize

### 1.2 Get Database Credentials
```bash
# From Supabase Dashboard > Settings > Database
DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"
SUPABASE_URL="https://[project-ref].supabase.co"
SUPABASE_ANON_KEY="[anon-key]"
SUPABASE_SERVICE_ROLE_KEY="[service-role-key]"
```

### 1.3 Update Database Schema
```bash
# Update prisma/schema.prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

# Run migration
npx prisma migrate deploy
```

## 🔐 Step 2: Authentication Setup

### 2.1 NextAuth Secret
```bash
# Generate secure secret
openssl rand -base64 32
# Add to environment
NEXTAUTH_SECRET="[generated-secret]"
NEXTAUTH_URL="https://your-domain.com"
```

### 2.2 Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://your-domain.com/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (for testing)

```bash
GOOGLE_CLIENT_ID="[client-id].apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="[client-secret]"
```

### 2.3 GitHub OAuth Setup
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create new OAuth App
3. Set Authorization callback URL: `https://your-domain.com/api/auth/callback/github`

```bash
GITHUB_ID="[github-client-id]"
GITHUB_SECRET="[github-client-secret]"
```

## 💳 Step 3: Stripe Payment Setup

### 3.1 Create Stripe Account
1. Sign up at [stripe.com](https://stripe.com)
2. Complete account verification
3. Get API keys from Dashboard > Developers > API keys

```bash
STRIPE_PUBLISHABLE_KEY="pk_live_[key]"  # or pk_test_ for testing
STRIPE_SECRET_KEY="sk_live_[key]"       # or sk_test_ for testing
```

### 3.2 Create Products and Prices
1. Go to Stripe Dashboard > Products
2. Create two products:

**Premium Plan:**
- Name: "Premium"
- Price: $9.99/month
- Copy the Price ID

**Pro Plan:**
- Name: "Pro"  
- Price: $29.99/month
- Copy the Price ID

```bash
STRIPE_PREMIUM_PRICE_ID="price_[premium-price-id]"
STRIPE_PRO_PRICE_ID="price_[pro-price-id]"
```

### 3.3 Configure Webhooks
1. Go to Stripe Dashboard > Developers > Webhooks
2. Add endpoint: `https://your-domain.com/api/stripe/webhooks`
3. Select events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. Copy webhook signing secret

```bash
STRIPE_WEBHOOK_SECRET="whsec_[webhook-secret]"
```

## 📊 Step 4: Monitoring Setup (Optional)

### 4.1 Sentry Error Tracking
1. Create account at [sentry.io](https://sentry.io)
2. Create new project (Next.js)
3. Get DSN from project settings

```bash
NEXT_PUBLIC_SENTRY_DSN="https://[key]@[org].ingest.sentry.io/[project]"
```

### 4.2 PostHog Analytics
1. Create account at [posthog.com](https://posthog.com)
2. Get project API key

```bash
NEXT_PUBLIC_POSTHOG_KEY="phc_[key]"
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
```

## 🚀 Step 5: Deployment (Vercel)

### 5.1 Install Vercel CLI
```bash
npm install -g vercel
vercel login
```

### 5.2 Deploy Application
```bash
# From project directory
vercel

# Follow prompts:
# - Link to existing project? No
# - Project name: yt2gif
# - Directory: ./
# - Override settings? No
```

### 5.3 Configure Environment Variables
In Vercel Dashboard > Project > Settings > Environment Variables, add all variables from your `.env` file.

### 5.4 Set Custom Domain (Optional)
1. Go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS as instructed
4. Update `NEXTAUTH_URL` to use custom domain

## ✅ Step 6: Verification

### 6.1 Test Authentication
- [ ] Email/password registration works
- [ ] Google OAuth login works  
- [ ] GitHub OAuth login works
- [ ] User dashboard loads correctly
- [ ] Session persistence works

### 6.2 Test Payment System
- [ ] Upgrade flow works (use Stripe test cards)
- [ ] Webhooks receive events correctly
- [ ] User tier updates after payment
- [ ] Billing portal accessible
- [ ] Subscription cancellation works

### 6.3 Test Core Functionality
- [ ] YouTube URL processing works
- [ ] GIF generation completes
- [ ] Tier limits enforced correctly
- [ ] File downloads work
- [ ] Usage tracking updates

## 🔧 Troubleshooting

### Common Issues

**Database Connection:**
```bash
# Test connection
npx prisma db push
```

**OAuth Redirect Mismatch:**
- Ensure callback URLs match exactly
- Check NEXTAUTH_URL is correct

**Stripe Webhook Issues:**
- Verify webhook URL is accessible
- Check webhook secret matches
- Test with Stripe CLI: `stripe listen --forward-to localhost:3000/api/stripe/webhooks`

**Build Failures:**
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

## 📞 Support

If you encounter issues:
1. Check the logs in your deployment platform
2. Verify all environment variables are set correctly
3. Test individual components (auth, payments, core features)
4. Review the troubleshooting section in DEPLOYMENT.md

Your production-ready yt2gif.app is now configured! 🎉
