# 🗄️ Database Setup Guide for yt2gif.app

This guide covers setting up your production database for yt2gif.app, including migration from SQLite to PostgreSQL.

## 📋 Database Options

### Recommended: Supabase (PostgreSQL)
- **Pros**: Easy setup, built-in auth, real-time features, generous free tier
- **Cons**: Vendor lock-in
- **Best for**: Most production deployments

### Alternative: PlanetScale (MySQL)
- **Pros**: Serverless, branching, excellent scaling
- **Cons**: MySQL instead of PostgreSQL, pricing
- **Best for**: High-scale applications

### Self-hosted PostgreSQL
- **Pros**: Full control, cost-effective at scale
- **Cons**: Requires database administration
- **Best for**: Enterprise or custom deployments

## 🚀 Quick Setup with Supabase

### Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `yt2gif-production`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for project initialization (2-3 minutes)

### Step 2: Get Connection Details

From your Supabase dashboard:

1. Go to **Settings** → **Database**
2. Copy the connection string:
   ```
   postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
   ```
3. Go to **Settings** → **API**
4. Copy the Project URL and API keys

### Step 3: Configure Environment Variables

Add to your `.env.production` file:

```bash
# Database
DATABASE_PROVIDER="postgresql"
DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"

# Supabase (optional - for additional features)
SUPABASE_URL="https://[PROJECT-REF].supabase.co"
SUPABASE_ANON_KEY="[ANON-KEY]"
SUPABASE_SERVICE_ROLE_KEY="[SERVICE-ROLE-KEY]"
```

### Step 4: Run Database Migration

```bash
# Install dependencies
npm install

# Run the migration script
npm run db:migrate:production
```

## 🔄 Migration from SQLite to PostgreSQL

If you have existing data in SQLite that you want to migrate:

### Option 1: Automated Migration Script

```bash
# Backup your current SQLite database
cp prisma/dev.db prisma/dev.db.backup

# Set up PostgreSQL environment variables
export DATABASE_PROVIDER="postgresql"
export DATABASE_URL="your-postgresql-connection-string"

# Run migration
npm run db:migrate:production
```

### Option 2: Manual Data Export/Import

1. **Export data from SQLite:**
   ```bash
   # Create a data export script
   node -e "
   const { PrismaClient } = require('@prisma/client');
   const fs = require('fs');
   
   async function exportData() {
     const prisma = new PrismaClient();
     
     const users = await prisma.user.findMany({
       include: { gifs: true, accounts: true }
     });
     
     fs.writeFileSync('data-export.json', JSON.stringify(users, null, 2));
     console.log('Data exported to data-export.json');
     
     await prisma.\$disconnect();
   }
   
   exportData();
   "
   ```

2. **Import data to PostgreSQL:**
   ```bash
   # After setting up PostgreSQL, run import
   node -e "
   const { PrismaClient } = require('@prisma/client');
   const fs = require('fs');
   
   async function importData() {
     const prisma = new PrismaClient();
     const users = JSON.parse(fs.readFileSync('data-export.json'));
     
     for (const user of users) {
       const { gifs, accounts, ...userData } = user;
       
       const newUser = await prisma.user.create({
         data: userData
       });
       
       // Import associated data...
     }
     
     await prisma.\$disconnect();
   }
   
   importData();
   "
   ```

## 🔧 Database Configuration

### Connection Pooling

For production, configure connection pooling in your `DATABASE_URL`:

```bash
# Add connection pool parameters
DATABASE_URL="********************************/db?pgbouncer=true&connection_limit=10"
```

### Performance Optimization

1. **Indexes**: The schema includes necessary indexes for performance
2. **Connection Limits**: Set appropriate connection limits based on your hosting platform
3. **Query Optimization**: Monitor slow queries and optimize as needed

### Backup Strategy

#### Supabase Automatic Backups
- Supabase provides automatic daily backups
- Backups are retained for 7 days on free tier, 30 days on paid plans
- Access backups from Dashboard → Settings → Database → Backups

#### Manual Backup Script
```bash
# Create backup script
cat > scripts/backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_FILE="backup-$(date +%Y%m%d-%H%M%S).sql"
pg_dump $DATABASE_URL > $BACKUP_FILE
echo "Database backed up to $BACKUP_FILE"
EOF

chmod +x scripts/backup-db.sh
```

## 🛡️ Security Best Practices

### Database Security
1. **Use strong passwords** (minimum 16 characters)
2. **Enable SSL** connections (enabled by default on Supabase)
3. **Restrict network access** to your application servers only
4. **Regular security updates** (handled by Supabase)
5. **Monitor access logs** for suspicious activity

### Application Security
1. **Use connection pooling** to prevent connection exhaustion
2. **Validate all inputs** before database queries
3. **Use parameterized queries** (Prisma handles this automatically)
4. **Implement rate limiting** on database-heavy operations
5. **Monitor query performance** and set timeouts

## 📊 Monitoring and Maintenance

### Key Metrics to Monitor
- **Connection count**: Ensure you don't exceed limits
- **Query performance**: Monitor slow queries
- **Database size**: Track growth and plan for scaling
- **Error rates**: Monitor failed connections and queries

### Supabase Dashboard
- **Database**: View tables, run queries, manage indexes
- **API**: Monitor API usage and performance
- **Auth**: Manage authentication settings
- **Storage**: Handle file uploads (if using Supabase storage)

### Regular Maintenance Tasks
- **Weekly**: Review query performance and error logs
- **Monthly**: Analyze database growth and optimize storage
- **Quarterly**: Review and update backup/recovery procedures

## 🚨 Troubleshooting

### Common Issues

**Connection Refused:**
```bash
# Check if DATABASE_URL is correct
echo $DATABASE_URL

# Test connection manually
psql $DATABASE_URL -c "SELECT 1;"
```

**Migration Failures:**
```bash
# Reset migrations (CAUTION: This will lose data)
npx prisma migrate reset

# Or force push schema
npx prisma db push --force-reset
```

**Performance Issues:**
```bash
# Check for missing indexes
npx prisma db pull
npx prisma generate

# Analyze query performance in Supabase dashboard
```

### Getting Help

1. **Supabase Support**: Available through dashboard chat
2. **Prisma Documentation**: [prisma.io/docs](https://prisma.io/docs)
3. **Community Forums**: GitHub discussions and Discord
4. **Database Logs**: Check Supabase logs for detailed error information

## 📈 Scaling Considerations

### When to Scale Up
- Connection pool exhaustion
- Slow query performance
- High CPU/memory usage
- Storage approaching limits

### Scaling Options
1. **Vertical Scaling**: Upgrade to larger Supabase plan
2. **Read Replicas**: For read-heavy workloads
3. **Connection Pooling**: Use PgBouncer for better connection management
4. **Caching**: Implement Redis for frequently accessed data

Your database is now ready for production! 🎉
