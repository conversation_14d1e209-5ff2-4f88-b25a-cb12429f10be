#!/usr/bin/env node

/**
 * Database Migration Script for yt2gif.app
 * 
 * This script helps migrate from SQLite development to PostgreSQL production
 * and sets up the database schema for production deployment.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, 'green');
const error = (message) => log(`❌ ${message}`, 'red');
const warning = (message) => log(`⚠️  ${message}`, 'yellow');
const info = (message) => log(`ℹ️  ${message}`, 'blue');

// Execute command with error handling
const execCommand = (command, description) => {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const output = execSync(command, { 
      stdio: 'pipe',
      encoding: 'utf8'
    });
    success(`${description} completed`);
    return output;
  } catch (err) {
    error(`${description} failed: ${err.message}`);
    throw err;
  }
};

// Check if environment variables are set
const checkEnvironment = () => {
  log('\n🔍 Checking Environment Configuration...', 'bold');
  
  const requiredVars = [
    'DATABASE_URL',
    'DATABASE_PROVIDER'
  ];

  let allSet = true;
  
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      error(`Missing environment variable: ${varName}`);
      allSet = false;
    } else {
      success(`${varName} is configured`);
    }
  }

  if (!allSet) {
    error('Please set all required environment variables before running migration');
    process.exit(1);
  }

  // Check database provider
  const provider = process.env.DATABASE_PROVIDER;
  if (provider !== 'postgresql' && provider !== 'sqlite') {
    error(`Unsupported database provider: ${provider}. Use 'postgresql' or 'sqlite'`);
    process.exit(1);
  }

  info(`Database provider: ${provider}`);
  
  // Validate PostgreSQL URL format for production
  if (provider === 'postgresql') {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl.startsWith('postgresql://') && !dbUrl.startsWith('postgres://')) {
      error('DATABASE_URL must be a valid PostgreSQL connection string');
      process.exit(1);
    }
  }
};

// Backup existing SQLite database if it exists
const backupSQLiteData = () => {
  const sqliteDbPath = './prisma/dev.db';
  
  if (fs.existsSync(sqliteDbPath)) {
    log('\n💾 Backing up SQLite database...', 'bold');
    
    const backupPath = `./prisma/dev.db.backup.${Date.now()}`;
    fs.copyFileSync(sqliteDbPath, backupPath);
    success(`SQLite database backed up to: ${backupPath}`);
    
    // Export data if there are existing records
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      // Check if there's data to export
      const userCount = await prisma.user.count();
      const gifCount = await prisma.gif.count();
      
      if (userCount > 0 || gifCount > 0) {
        warning(`Found ${userCount} users and ${gifCount} GIFs in SQLite database`);
        info('Consider exporting this data before switching to PostgreSQL');
        info('You can use: npx prisma db seed or custom migration scripts');
      }
      
      await prisma.$disconnect();
    } catch (err) {
      warning('Could not check existing data in SQLite database');
    }
  } else {
    info('No existing SQLite database found - starting fresh');
  }
};

// Generate Prisma client
const generatePrismaClient = () => {
  execCommand('npx prisma generate', 'Generating Prisma client');
};

// Create initial migration
const createInitialMigration = () => {
  log('\n📝 Creating Database Migration...', 'bold');
  
  const provider = process.env.DATABASE_PROVIDER;
  
  if (provider === 'postgresql') {
    // For PostgreSQL, create a proper migration
    try {
      execCommand(
        'npx prisma migrate dev --name init --create-only',
        'Creating initial migration'
      );
      
      success('Migration files created successfully');
      info('Review the migration files in prisma/migrations/ before applying');
      
    } catch (err) {
      warning('Migration creation failed, trying db push instead');
      execCommand('npx prisma db push', 'Pushing schema to database');
    }
  } else {
    // For SQLite, just push the schema
    execCommand('npx prisma db push', 'Pushing schema to SQLite database');
  }
};

// Apply migrations to production database
const applyMigrations = () => {
  log('\n🚀 Applying Migrations to Database...', 'bold');
  
  const provider = process.env.DATABASE_PROVIDER;
  
  if (provider === 'postgresql') {
    try {
      // First try to apply existing migrations
      execCommand('npx prisma migrate deploy', 'Applying migrations to production database');
    } catch (err) {
      warning('Migration deploy failed, trying db push');
      execCommand('npx prisma db push', 'Pushing schema to production database');
    }
  } else {
    execCommand('npx prisma db push', 'Updating SQLite database schema');
  }
};

// Verify database connection and schema
const verifyDatabase = async () => {
  log('\n✅ Verifying Database Setup...', 'bold');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // Test basic connection
    await prisma.$queryRaw`SELECT 1`;
    success('Database connection successful');
    
    // Check if tables exist by counting users
    const userCount = await prisma.user.count();
    success(`Database schema verified (${userCount} users found)`);
    
    // Test creating a test record (and immediately delete it)
    const testUser = await prisma.user.create({
      data: {
        email: `test-${Date.now()}@example.com`,
        name: 'Migration Test User'
      }
    });
    
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    
    success('Database write operations working correctly');
    
    await prisma.$disconnect();
    
  } catch (err) {
    error(`Database verification failed: ${err.message}`);
    throw err;
  }
};

// Main migration function
async function runMigration() {
  log('\n🗄️ Database Migration for yt2gif.app\n', 'bold');
  
  try {
    // Step 1: Check environment
    checkEnvironment();
    
    // Step 2: Backup existing data
    await backupSQLiteData();
    
    // Step 3: Generate Prisma client
    generatePrismaClient();
    
    // Step 4: Create migration
    createInitialMigration();
    
    // Step 5: Apply migrations
    applyMigrations();
    
    // Step 6: Verify setup
    await verifyDatabase();
    
    // Success message
    log('\n🎉 Database Migration Completed Successfully!', 'green');
    
    log('\n📋 Next Steps:', 'blue');
    info('1. Test your application with the new database');
    info('2. If migrating from SQLite, consider importing existing data');
    info('3. Set up database backups for production');
    info('4. Configure connection pooling if needed');
    info('5. Monitor database performance and optimize as needed');
    
    if (process.env.DATABASE_PROVIDER === 'postgresql') {
      log('\n🔗 PostgreSQL Production Tips:', 'blue');
      info('• Enable connection pooling for better performance');
      info('• Set up automated backups');
      info('• Monitor query performance');
      info('• Consider read replicas for high traffic');
    }
    
  } catch (err) {
    error(`\nMigration failed: ${err.message}`);
    
    log('\n🔧 Troubleshooting Tips:', 'yellow');
    info('1. Verify DATABASE_URL is correct and accessible');
    info('2. Ensure database server is running');
    info('3. Check network connectivity to database');
    info('4. Verify database user has proper permissions');
    info('5. Review Prisma schema for any syntax errors');
    
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  log('\n📖 Database Migration Script Help\n', 'bold');
  log('Usage: npm run db:migrate:production [options]');
  log('\nOptions:');
  log('  --help, -h     Show this help message');
  log('  --dry-run      Show what would be done without executing');
  log('\nEnvironment Variables Required:');
  log('  DATABASE_URL      Database connection string');
  log('  DATABASE_PROVIDER Database provider (postgresql or sqlite)');
  log('\nExamples:');
  log('  # Migrate to PostgreSQL');
  log('  DATABASE_PROVIDER=postgresql DATABASE_URL="postgresql://..." npm run db:migrate:production');
  log('  # Update SQLite schema');
  log('  DATABASE_PROVIDER=sqlite DATABASE_URL="file:./prisma/dev.db" npm run db:migrate:production');
  process.exit(0);
}

if (args.includes('--dry-run')) {
  log('\n🔍 Dry Run Mode - No changes will be made\n', 'yellow');
  log('Would execute the following steps:');
  log('1. Check environment variables');
  log('2. Backup existing SQLite database (if exists)');
  log('3. Generate Prisma client');
  log('4. Create database migration');
  log('5. Apply migration to database');
  log('6. Verify database setup');
  log('\nTo run the actual migration, remove --dry-run flag');
  process.exit(0);
}

// Run the migration
runMigration().catch((err) => {
  error(`Unexpected error: ${err.message}`);
  process.exit(1);
});
