#!/usr/bin/env node

/**
 * Production Readiness Checker for yt2gif.app
 * 
 * This script verifies that all required environment variables and services
 * are properly configured for production deployment.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Helper functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, 'green');
const error = (message) => log(`❌ ${message}`, 'red');
const warning = (message) => log(`⚠️  ${message}`, 'yellow');
const info = (message) => log(`ℹ️  ${message}`, 'blue');

// Check if environment variable exists and is not empty
const checkEnvVar = (name, required = true) => {
  const value = process.env[name];
  if (!value || value.trim() === '') {
    if (required) {
      error(`Missing required environment variable: ${name}`);
      return false;
    } else {
      warning(`Optional environment variable not set: ${name}`);
      return true;
    }
  }
  success(`${name} is configured`);
  return true;
};

// Check if a URL is valid
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// Main checker function
async function runProductionCheck() {
  log('\n🚀 Production Readiness Check for yt2gif.app\n', 'bold');
  
  let allChecksPass = true;
  let criticalIssues = 0;
  let warnings = 0;

  // Database Configuration
  log('📊 Checking Database Configuration...', 'blue');
  if (!checkEnvVar('DATABASE_URL')) {
    criticalIssues++;
    allChecksPass = false;
  } else {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl.includes('postgresql://')) {
      error('DATABASE_URL should use PostgreSQL for production');
      criticalIssues++;
      allChecksPass = false;
    }
  }

  // NextAuth Configuration
  log('\n🔐 Checking Authentication Configuration...', 'blue');
  if (!checkEnvVar('NEXTAUTH_SECRET')) {
    criticalIssues++;
    allChecksPass = false;
  } else {
    const secret = process.env.NEXTAUTH_SECRET;
    if (secret.length < 32) {
      error('NEXTAUTH_SECRET should be at least 32 characters long');
      criticalIssues++;
      allChecksPass = false;
    }
  }

  if (!checkEnvVar('NEXTAUTH_URL')) {
    criticalIssues++;
    allChecksPass = false;
  } else {
    const authUrl = process.env.NEXTAUTH_URL;
    if (!isValidUrl(authUrl)) {
      error('NEXTAUTH_URL is not a valid URL');
      criticalIssues++;
      allChecksPass = false;
    } else if (!authUrl.startsWith('https://') && process.env.NODE_ENV === 'production') {
      error('NEXTAUTH_URL must use HTTPS in production');
      criticalIssues++;
      allChecksPass = false;
    }
  }

  // OAuth Providers
  log('\n🔑 Checking OAuth Providers...', 'blue');
  const hasGoogle = checkEnvVar('GOOGLE_CLIENT_ID', false) && checkEnvVar('GOOGLE_CLIENT_SECRET', false);
  const hasGitHub = checkEnvVar('GITHUB_ID', false) && checkEnvVar('GITHUB_SECRET', false);
  
  if (!hasGoogle && !hasGitHub) {
    warning('No OAuth providers configured. Users can only use email/password authentication.');
    warnings++;
  }

  // Stripe Configuration
  log('\n💳 Checking Stripe Configuration...', 'blue');
  const stripeChecks = [
    checkEnvVar('STRIPE_PUBLISHABLE_KEY'),
    checkEnvVar('STRIPE_SECRET_KEY'),
    checkEnvVar('STRIPE_WEBHOOK_SECRET'),
    checkEnvVar('STRIPE_PREMIUM_PRICE_ID'),
    checkEnvVar('STRIPE_PRO_PRICE_ID')
  ];

  if (!stripeChecks.every(check => check)) {
    criticalIssues++;
    allChecksPass = false;
  } else {
    // Check if using live keys in production
    const pubKey = process.env.STRIPE_PUBLISHABLE_KEY;
    const secretKey = process.env.STRIPE_SECRET_KEY;
    
    if (process.env.NODE_ENV === 'production') {
      if (!pubKey.startsWith('pk_live_') || !secretKey.startsWith('sk_live_')) {
        warning('Using Stripe test keys in production environment');
        warnings++;
      }
    }
  }

  // Monitoring Configuration
  log('\n📊 Checking Monitoring Configuration...', 'blue');
  checkEnvVar('NEXT_PUBLIC_SENTRY_DSN', false);
  checkEnvVar('NEXT_PUBLIC_POSTHOG_KEY', false);

  // Application Configuration
  log('\n🌐 Checking Application Configuration...', 'blue');
  checkEnvVar('NODE_ENV');
  checkEnvVar('NEXT_PUBLIC_BASE_URL');

  // File System Checks
  log('\n📁 Checking File System...', 'blue');
  const tempDir = process.env.TEMP_DIR || './temp';
  if (!fs.existsSync(tempDir)) {
    try {
      fs.mkdirSync(tempDir, { recursive: true });
      success(`Created temp directory: ${tempDir}`);
    } catch (err) {
      error(`Cannot create temp directory: ${tempDir}`);
      criticalIssues++;
      allChecksPass = false;
    }
  } else {
    success(`Temp directory exists: ${tempDir}`);
  }

  // Database Schema Check
  log('\n🗄️ Checking Database Schema...', 'blue');
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // Try to connect and run a simple query
    await prisma.$queryRaw`SELECT 1`;
    success('Database connection successful');
    
    // Check if tables exist
    const userCount = await prisma.user.count();
    success(`Database schema is ready (${userCount} users)`);
    
    await prisma.$disconnect();
  } catch (err) {
    error(`Database connection failed: ${err.message}`);
    criticalIssues++;
    allChecksPass = false;
  }

  // Security Checks
  log('\n🛡️ Security Checks...', 'blue');
  if (process.env.NODE_ENV !== 'production') {
    warning('NODE_ENV is not set to "production"');
    warnings++;
  }

  // Summary
  log('\n📋 Summary:', 'bold');
  if (allChecksPass && criticalIssues === 0) {
    success('🎉 All critical checks passed! Your application is ready for production.');
  } else {
    error(`❌ ${criticalIssues} critical issue(s) found. Please fix before deploying.`);
  }

  if (warnings > 0) {
    warning(`⚠️  ${warnings} warning(s) found. Consider addressing these for optimal production setup.`);
  }

  log('\n📚 Next Steps:', 'blue');
  if (criticalIssues > 0) {
    info('1. Fix all critical issues listed above');
    info('2. Re-run this check: npm run production:check');
    info('3. Test your application locally with production environment');
    info('4. Deploy to your production environment');
  } else {
    info('1. Deploy your application to production');
    info('2. Test all functionality in production environment');
    info('3. Set up monitoring and alerts');
    info('4. Configure domain and SSL certificate');
  }

  log('\n📖 For detailed setup instructions, see PRODUCTION_SETUP.md\n');

  // Exit with appropriate code
  process.exit(criticalIssues > 0 ? 1 : 0);
}

// Run the check
runProductionCheck().catch((err) => {
  error(`Unexpected error during production check: ${err.message}`);
  process.exit(1);
});
