{"build": {"env": {"ENABLE_EXPERIMENTAL_COREPACK": "1"}}, "functions": {"src/app/api/process/route.ts": {"maxDuration": 300}, "src/app/api/download/*/route.ts": {"maxDuration": 60}}, "rewrites": [{"source": "/ingest/static/(.*)", "destination": "https://us-assets.i.posthog.com/static/$1"}, {"source": "/ingest/(.*)", "destination": "https://us.i.posthog.com/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate"}]}]}