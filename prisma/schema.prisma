// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
  // Change to "postgresql" when using Supabase
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For credentials login
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Subscription tier
  tier          String    @default("FREE")
  
  // Stripe subscription info
  stripeCustomerId       String?   @unique
  stripeSubscriptionId   String?   @unique
  stripePriceId          String?
  stripeCurrentPeriodEnd DateTime?
  
  // Usage tracking
  gifsCreated   Int       @default(0)
  storageUsed   Int       @default(0) // in bytes
  lastResetDate DateTime  @default(now())
  
  accounts Account[]
  sessions Session[]
  gifs     Gif[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Custom models for yt2gif
model Gif {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Video source
  youtubeUrl  String
  videoId     String
  videoTitle  String
  
  // GIF details
  fileName    String
  filePath    String
  fileSize    Int
  duration    Float
  startTime   Float
  endTime     Float
  quality     String
  hasWatermark Boolean @default(true)
  
  // User relation
  userId      String?
  user        User?   @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  // Analytics
  downloads   Int     @default(0)
  views       Int     @default(0)
}

// Note: Using strings instead of enums for SQLite compatibility
// UserTier: "FREE", "PREMIUM", "PRO"
// GifQuality: "LOW", "HIGH"
