{"name": "yt2gif", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "build:analyze": "prisma generate && ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .next out node_modules/.cache", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "performance:audit": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "size:check": "npm run build && npx bundlesize", "db:migrate:prod": "prisma migrate deploy", "prestart": "npm run db:generate", "health": "curl -f http://localhost:3000/api/health || exit 1", "deploy:check": "./deploy.sh check", "deploy:build": "./deploy.sh build", "deploy:vercel": "./deploy.sh vercel", "deploy:docker": "./deploy.sh docker", "deploy:migrate": "./deploy.sh migrate", "postinstall": "prisma generate"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@opentelemetry/api": "^1.9.0", "@opentelemetry/core": "^2.0.1", "@opentelemetry/instrumentation": "^0.203.0", "@prisma/client": "^6.12.0", "@sentry/nextjs": "^9.40.0", "@supabase/supabase-js": "^2.52.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.2.0", "lucide-react": "^0.468.0", "micro": "^10.0.1", "next": "15.1.6", "next-auth": "^4.24.10", "posthog-js": "^1.257.1", "posthog-node": "^4.18.0", "react": "19.0.0", "react-dom": "19.0.0", "stripe": "^14.25.0", "tailwind-merge": "^2.5.4", "uuid": "^10.0.0", "ytdl-core": "^4.11.5", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/fluent-ffmpeg": "^2.1.26", "@types/fs-extra": "^11.0.4", "@types/micro": "^7.3.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "critters": "^0.0.23", "eslint": "^8", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.3.3", "prisma": "^6.12.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}