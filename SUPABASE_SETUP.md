# Supabase Setup Guide

This guide will help you set up Supabase for the yt2gif application.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: yt2gif
   - **Database Password**: Generate a strong password and save it
   - **Region**: Choose the region closest to your users
5. Click "Create new project"

## 2. Get Your Project Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like `https://your-project.supabase.co`)
   - **anon/public key** (starts with `eyJ...`)
   - **service_role key** (starts with `eyJ...`)

## 3. Update Environment Variables

Add these to your `.env.local` file:

```bash
# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key-here"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key-here"

# Database URL for Prisma
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

## 4. Run Database Migrations

Once you have your Supabase database URL:

```bash
# Generate Prisma client for PostgreSQL
npm run db:generate

# Push the schema to Supabase
npm run db:push
```

## 5. Enable Authentication in Supabase

1. Go to **Authentication** → **Settings**
2. Configure your site URL: `http://localhost:3000` (for development)
3. Add redirect URLs:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/github`

## 6. Set up OAuth Providers (Optional)

### Google OAuth:
1. Go to **Authentication** → **Providers**
2. Enable Google provider
3. Add your Google Client ID and Secret

### GitHub OAuth:
1. Go to **Authentication** → **Providers**
2. Enable GitHub provider
3. Add your GitHub Client ID and Secret

## 7. Row Level Security (RLS)

For production, you should enable RLS on your tables:

```sql
-- Enable RLS on the User table
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only access their own data
CREATE POLICY "Users can only access their own data" ON "User"
  FOR ALL USING (auth.uid()::text = id);

-- Enable RLS on the Gif table
ALTER TABLE "Gif" ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only access their own GIFs
CREATE POLICY "Users can only access their own GIFs" ON "Gif"
  FOR ALL USING (auth.uid()::text = "userId");
```

## 8. Test the Connection

```bash
npm run dev
```

Visit `http://localhost:3000/api/auth/providers` to verify authentication is working.

## Migration from SQLite

The application has been updated to support both SQLite (for development) and PostgreSQL (for production with Supabase).

- **Development**: Use SQLite with `DATABASE_URL="file:./prisma/dev.db"`
- **Production**: Use Supabase PostgreSQL with the connection string from step 3

## Troubleshooting

1. **Connection Issues**: Verify your DATABASE_URL is correct and includes the password
2. **Migration Errors**: Make sure your Supabase project is active and the database is accessible
3. **Auth Issues**: Check that your redirect URLs match exactly in both Supabase and your OAuth providers

## Production Deployment

For production deployment:

1. Set the `NEXTAUTH_URL` to your production domain
2. Update redirect URLs in Supabase and OAuth providers
3. Enable RLS policies for security
4. Consider setting up database backups