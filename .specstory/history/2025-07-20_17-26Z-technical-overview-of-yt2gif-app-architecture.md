<!-- Generated by SpecStory -->

# Technical overview of yt2gif.app architecture (2025-07-20 17:26Z)

_**User**_

Can you give me a technical overview of this projects artchitecture

---

_**Assistant**_

Certainly! Here’s a technical overview of the yt2gif.app architecture:

## Executive Summary

yt2gif.app is a modern web application built with Next.js 15, enabling users to convert YouTube videos into shareable GIFs. It uses a freemium business model, robust user management, payment processing, and scalable video processing infrastructure.

## Technology Stack

- **Frontend:** Next.js 15 (React, App Router), TypeScript, Tailwind CSS, Lucide React icons
- **State Management:** React Context or Zustand
- **Authentication:** NextAuth.js (Google, GitHub, email), Prisma Adapter
- **Backend:** Node.js, Next.js API Routes (serverless), Edge Runtime
- **Database:** SQLite (dev), PostgreSQL (prod) via Prisma ORM
- **File Storage:** Local (dev), AWS S3 (prod)
- **Video Processing:** FFmpeg (via fluent-ffmpeg), yt-dlp, gifsicle, Canvas API/ImageMagick
- **Queue System:** Redis + Bull for background jobs
- **Payment:** Stripe integration

## System Architecture

### Application Structure

```
src/
├── app/          # Next.js App Router pages (frontend & API)
├── components/   # Reusable UI components
├── lib/          # Utility functions/integrations
├── types/        # TypeScript type definitions
└── prisma/       # Database schema/migrations
```

### Component Architecture

- **YoutubeUrlInput:** Video URL validation/submission
- **VideoPreview:** HTML5 video player
- **RangeSelector:** Timeline for GIF selection
- **ProcessingStatus:** Conversion progress
- **GifResult:** Preview/download interface
- **ErrorBoundary, LoadingStates, ToastProvider, AnalyticsProvider:** Infrastructure components

### Data Flow

1. User submits YouTube URL
2. Metadata extraction/validation
3. User selects start/end times
4. Background GIF generation (async)
5. Result delivered (download link/preview)

## Performance & Optimization

- **Frontend:** Code splitting, lazy loading, image optimization, CDN caching
- **Backend:** FFmpeg presets, memory management, concurrent processing
- **Database:** Prisma ORM, connection pooling, indexing

## Monitoring & Analytics

- **Error Monitoring:** Sentry (client/server)
- **User Analytics:** PostHog (event tracking, feature flags, user journeys)
- **Business Intelligence:** Conversion tracking, usage patterns, KPIs

## Scalability

- **Stateless API:** RESTful endpoints, JWT sessions
- **Horizontal Scaling:** Containerized FFmpeg, load balancing
- **CDN:** Global asset distribution
- **Background Processing:** Redis queue, scalable workers

## Security

- **Input Validation:** Zod schemas, URL/file validation
- **User Data:** bcrypt password hashing, JWT sessions, encryption, GDPR compliance
- **Payment:** Stripe (PCI DSS), webhook security, audit logging

## Deployment

- **Development:** SQLite, hot reloading, isolated configs
- **Production:** Vercel (serverless), PostgreSQL, automated backups, global edge network

## API Architecture

- **RESTful Endpoints:** Predictable URLs, proper HTTP verbs, consistent error handling
- **Authentication:** JWT bearer tokens, role-based access, rate limiting

## Technology Decisions

- **Next.js 15:** Full-stack capability, performance, developer experience
- **TypeScript:** Type safety, code quality
- **Prisma ORM:** Type-safe queries, migration management
- **SQLite → PostgreSQL:** Simple dev, scalable prod

For more details, see `ARCHITECTURE.md`.

---

_**User**_

in the TESTING_CHECKLIST.MD file, we have created the env.local file and checked all boxes in the Enviornment Setup section. We are now in Basic Enviornment Validation section. please assist me with the four items in this section

---

_**Assistant**_

Certainly! Here’s how to proceed with the **Basic Environment Validation** section:

### 1. Check console for any startup errors
- Start your dev server:  
  ```bash
  npm run dev
  ```
- Watch your terminal and browser console for errors or warnings.  
  - Terminal: Look for red error messages.
  - Browser: Open DevTools (F12), check the Console tab for errors.

### 2. Verify database connection (no Prisma errors)
- On server startup, confirm there are no Prisma-related errors in the terminal.
- Try a basic app action that reads/writes to the database (e.g., register/login, create GIF).
- If you see errors like `PrismaClientInitializationError`, check your database config in `.env.local`.

### 3. Confirm all environment variables are loaded
- In your terminal, check for warnings about missing environment variables.
- Optionally, add a debug log in your code to print `process.env` (remove after testing).
- Ensure variables like `DATABASE_URL`, `NEXTAUTH_SECRET`, etc. are present and correct.

### 4. Test hot reload by making a small change
- Edit a file in `` (e.g., change some text in the homepage).
- Save the file and verify the browser updates automatically without a full reload.
- If hot reload doesn’t work, restart the dev server and check for errors.

Let me know if you encounter any issues during these steps!

---

