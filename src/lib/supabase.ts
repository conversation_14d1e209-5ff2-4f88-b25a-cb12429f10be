import { createClient } from '@supabase/supabase-js'

// Create a single supabase client for interacting with your database
export const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_ANON_KEY || ''
)

// Create a Supabase client with the service role key for admin operations
export const supabaseAdmin = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Types for the database tables (you can generate these with `supabase gen types typescript`)
export interface Database {
  public: {
    Tables: {
      // Your table definitions will go here
      // These can be auto-generated from your Supabase schema
    }
    Views: {
      // Your views definitions
    }
    Functions: {
      // Your functions definitions
    }
    Enums: {
      // Your enums definitions
    }
  }
}