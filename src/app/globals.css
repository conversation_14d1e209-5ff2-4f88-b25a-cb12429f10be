@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile optimizations */
* {
  -webkit-tap-highlight-color: transparent;
}

input, button, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Improve touch target sizes on mobile */
@media (pointer: coarse) {
  button, [role="button"], input[type="submit"], input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}