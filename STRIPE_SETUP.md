# 💳 Stripe Payment Setup Guide for yt2gif.app

This guide walks you through setting up Stripe payments for your yt2gif.app production deployment.

## 📋 Prerequisites

- Stripe account (sign up at [stripe.com](https://stripe.com))
- Business information for account verification
- Bank account for payouts (required for live mode)

## 🚀 Step 1: Stripe Account Setup

### 1.1 Create Stripe Account
1. Go to [stripe.com](https://stripe.com) and click "Start now"
2. Enter your email and create a password
3. Verify your email address
4. Complete business information:
   - Business type (Individual/Company)
   - Business details
   - Bank account information
   - Tax information

### 1.2 Account Verification
- **Test Mode**: Available immediately
- **Live Mode**: Requires business verification (1-7 days)
- Start with test mode for development and testing

## 🔑 Step 2: Get API Keys

### 2.1 Access API Keys
1. Go to Stripe Dashboard
2. Navigate to **Developers** → **API keys**
3. You'll see two sets of keys:

**Test Keys (for development):**
```bash
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
```

**Live Keys (for production):**
```bash
STRIPE_PUBLISHABLE_KEY="pk_live_..."
STRIPE_SECRET_KEY="sk_live_..."
```

### 2.2 Security Best Practices
- Never commit secret keys to version control
- Use environment variables for all keys
- Rotate keys if compromised
- Use test keys during development

## 🛍️ Step 3: Create Products and Prices

### 3.1 Create Premium Product
1. Go to **Products** in Stripe Dashboard
2. Click **Add product**
3. Fill in details:
   - **Name**: `Premium`
   - **Description**: `Premium yt2gif plan with enhanced features`
   - **Image**: Upload your logo/product image (optional)

4. Add pricing:
   - **Price**: `$9.99`
   - **Billing period**: `Monthly`
   - **Currency**: `USD`
   - Click **Save product**

5. Copy the **Price ID** (starts with `price_`)

### 3.2 Create Pro Product
1. Click **Add product** again
2. Fill in details:
   - **Name**: `Pro`
   - **Description**: `Professional yt2gif plan with unlimited features`
   - **Image**: Upload your logo/product image (optional)

3. Add pricing:
   - **Price**: `$29.99`
   - **Billing period**: `Monthly`
   - **Currency**: `USD`
   - Click **Save product**

4. Copy the **Price ID** (starts with `price_`)

### 3.3 Configure Environment Variables
Add the Price IDs to your environment:

```bash
STRIPE_PREMIUM_PRICE_ID="price_1234567890abcdef"
STRIPE_PRO_PRICE_ID="price_0987654321fedcba"
```

## 🔗 Step 4: Configure Webhooks

Webhooks are crucial for handling subscription events and keeping your database in sync.

### 4.1 Create Webhook Endpoint
1. Go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Enter endpoint URL:
   - **Development**: `https://your-ngrok-url.ngrok.io/api/stripe/webhooks`
   - **Production**: `https://your-domain.com/api/stripe/webhooks`

### 4.2 Select Events
Choose these events to listen for:

**Customer Events:**
- `customer.created`
- `customer.updated`
- `customer.deleted`

**Subscription Events:**
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `customer.subscription.trial_will_end`

**Invoice Events:**
- `invoice.created`
- `invoice.finalized`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

**Checkout Events:**
- `checkout.session.completed`
- `checkout.session.expired`

### 4.3 Get Webhook Secret
1. After creating the webhook, click on it
2. Copy the **Signing secret** (starts with `whsec_`)
3. Add to environment variables:

```bash
STRIPE_WEBHOOK_SECRET="whsec_1234567890abcdef..."
```

## 🧪 Step 5: Testing Setup

### 5.1 Test Cards
Use these test card numbers for testing:

**Successful Payments:**
- `****************` (Visa)
- `****************` (Visa Debit)
- `****************` (Mastercard)

**Failed Payments:**
- `****************` (Card declined)
- `****************` (Insufficient funds)

**3D Secure:**
- `****************` (Requires authentication)

### 5.2 Test Webhooks Locally
1. Install Stripe CLI:
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # Linux
   wget https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.1_linux_x86_64.tar.gz
   tar -xvf stripe_1.19.1_linux_x86_64.tar.gz
   sudo mv stripe /usr/local/bin
   ```

2. Login to Stripe:
   ```bash
   stripe login
   ```

3. Forward webhooks to local development:
   ```bash
   stripe listen --forward-to localhost:3000/api/stripe/webhooks
   ```

4. Test webhook delivery:
   ```bash
   stripe trigger customer.subscription.created
   ```

### 5.3 Test Payment Flow
1. Start your application in development mode
2. Navigate to the pricing page
3. Click "Upgrade to Premium" or "Upgrade to Pro"
4. Use test card numbers to complete payment
5. Verify webhook events are received
6. Check that user tier is updated in database

## 🔒 Step 6: Security Configuration

### 6.1 Webhook Signature Verification
Your webhook handler should verify signatures:

<augment_code_snippet path="src/app/api/stripe/webhooks/route.ts" mode="EXCERPT">
````typescript
import { stripe } from '@/lib/stripe'
import { headers } from 'next/headers'

export async function POST(req: Request) {
  const body = await req.text()
  const signature = headers().get('stripe-signature')!
  
  let event
  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (err) {
    return new Response('Webhook signature verification failed', { status: 400 })
  }
  
  // Handle the event...
}
````
</augment_code_snippet>

### 6.2 Rate Limiting
Implement rate limiting on payment endpoints to prevent abuse.

### 6.3 Input Validation
Always validate and sanitize inputs before processing payments.

## 📊 Step 7: Production Checklist

### 7.1 Before Going Live
- [ ] Complete Stripe account verification
- [ ] Switch to live API keys
- [ ] Update webhook endpoints to production URLs
- [ ] Test complete payment flow with real cards
- [ ] Verify webhook events are processed correctly
- [ ] Set up monitoring and alerts
- [ ] Configure tax settings (if applicable)
- [ ] Set up payout schedule

### 7.2 Tax Configuration (Optional)
1. Go to **Settings** → **Tax**
2. Enable automatic tax calculation if needed
3. Configure tax rates for your jurisdictions

### 7.3 Monitoring Setup
1. **Stripe Dashboard**: Monitor payments, disputes, and metrics
2. **Webhook Logs**: Check webhook delivery status
3. **Application Logs**: Monitor payment processing errors
4. **Alerts**: Set up notifications for failed payments

## 🚨 Step 8: Troubleshooting

### Common Issues

**Webhook Not Receiving Events:**
```bash
# Check webhook endpoint is accessible
curl -X POST https://your-domain.com/api/stripe/webhooks

# Verify webhook secret is correct
echo $STRIPE_WEBHOOK_SECRET

# Check Stripe webhook logs in dashboard
```

**Payment Failures:**
- Check card details are valid
- Verify sufficient funds
- Check for 3D Secure requirements
- Review Stripe logs for detailed error messages

**Subscription Issues:**
- Verify Price IDs are correct
- Check customer has valid payment method
- Review subscription status in Stripe dashboard

### Testing Commands

```bash
# Test webhook locally
stripe listen --forward-to localhost:3000/api/stripe/webhooks

# Trigger test events
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
stripe trigger invoice.payment_failed

# Test API connection
curl -u sk_test_...: https://api.stripe.com/v1/customers
```

## 📈 Step 9: Analytics and Optimization

### Key Metrics to Track
- **Conversion Rate**: Visitors to paying customers
- **Churn Rate**: Monthly subscription cancellations
- **Revenue Growth**: Monthly recurring revenue (MRR)
- **Payment Success Rate**: Successful vs failed payments

### Stripe Analytics
- **Revenue**: Track MRR and growth trends
- **Customers**: Monitor customer acquisition and retention
- **Payments**: Analyze payment success rates and failures
- **Disputes**: Track chargebacks and disputes

### Optimization Tips
1. **Reduce Friction**: Minimize checkout steps
2. **Payment Methods**: Support multiple payment options
3. **Retry Logic**: Implement smart retry for failed payments
4. **Dunning Management**: Handle failed subscription payments
5. **Pricing Strategy**: A/B test different pricing models

Your Stripe payment system is now ready for production! 🎉

## 📞 Support Resources

- **Stripe Documentation**: [stripe.com/docs](https://stripe.com/docs)
- **Stripe Support**: Available through dashboard
- **Community**: [Stripe Discord](https://discord.gg/stripe)
- **Status Page**: [status.stripe.com](https://status.stripe.com)
