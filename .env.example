# Database
DATABASE_PROVIDER="postgresql"
DATABASE_URL="postgresql://postgres:password@localhost:5432/yt2gif"
# Supabase (for production)
SUPABASE_URL=""
SUPABASE_ANON_KEY=""
SUPABASE_SERVICE_ROLE_KEY=""

# Next.js
NEXTAUTH_URL="http://0.0.0.0:3000"
NEXTAUTH_SECRET="your-secret-here"

# Authentication Providers
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_ID=""
GITHUB_SECRET=""

# Stripe
STRIPE_PUBLISHABLE_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Analytics & Monitoring
NEXT_PUBLIC_POSTHOG_KEY=""
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
NEXT_PUBLIC_SENTRY_DSN=""

# App Configuration
NEXT_PUBLIC_BASE_URL="http://0.0.0.0:3000"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NODE_ENV="development"
FFMPEG_PATH="/usr/bin/ffmpeg"
MAX_VIDEO_DURATION_SECONDS="300"
MAX_FILE_SIZE_MB="100"
TEMP_DIR="./temp"

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MINUTES="15"

# File Storage (Optional - for CDN)
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION=""
AWS_S3_BUCKET=""

# Redis (optional - for production caching)
REDIS_URL=""

# Performance (Optional)
ANALYZE="false"